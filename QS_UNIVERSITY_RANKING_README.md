# QS世界大学排名数据同步功能

## 功能概述

本功能实现了从第三方API（咕咕数据）获取全球QS世界大学排名数据，并将数据保存到本地数据库中，支持定时同步和手动同步。

## 主要特性

1. **数据同步**：从咕咕数据API获取最新的QS世界大学排名数据
2. **定时任务**：支持定时自动同步数据（默认每天凌晨2点）
3. **手动同步**：支持通过管理界面手动触发数据同步
4. **数据管理**：支持查询、编辑、删除、导出排名数据
5. **API测试**：支持测试API连接状态
6. **数据清空**：支持清空所有排名数据

## 技术实现

### 数据库表结构

- **表名**：`qs_university_ranking`
- **主要字段**：
  - `university_id`：大学唯一标识
  - `university_name`：大学名称
  - `country`：所在国家
  - `region`：所在地区
  - `rank_overall`：综合排名
  - 各种排名指标和得分字段

### 核心组件

1. **实体类**：`QsUniversityRanking.java`
2. **数据访问层**：`QsUniversityRankingMapper.java` 和对应的XML文件
3. **业务逻辑层**：`IQsUniversityRankingService.java` 和实现类
4. **数据同步服务**：`QsDataSyncService.java`
5. **定时任务**：`QsDataSyncTask.java`
6. **控制器**：`QsUniversityRankingController.java`
7. **前端页面**：列表页、添加页、编辑页

### API配置

- **API地址**：`https://api.gugudata.com/metadata/global-university-ranking`
- **API密钥**：`3KPPMJEQK3EHL6PZDPXTGDEPMKQPZP4U`
- **分页大小**：20条/页

## 部署说明

### 1. 数据库初始化

执行 `sql/ry_20250416.sql` 文件中的SQL脚本，会自动创建：
- `qs_university_ranking` 数据表
- 相关菜单权限配置
- 定时任务配置

### 2. 编译部署

确保项目正常编译，所有依赖的类都能正确加载。

### 3. 访问功能

启动项目后，使用管理员账号登录，在"系统管理"菜单下可以看到"QS大学排名"菜单项。

## 使用说明

### 1. 数据同步

#### 手动同步
1. 进入"QS大学排名"页面
2. 点击"同步数据"按钮
3. 系统会从API获取所有数据并保存到数据库

#### 定时同步
- 系统默认配置每天凌晨2点自动同步数据
- 可在"系统监控" -> "定时任务"中修改同步频率

### 2. 数据管理

- **查询**：支持按大学名称、国家、地区、排名等条件查询
- **导出**：支持导出Excel格式的排名数据
- **编辑**：支持修改单条排名记录
- **删除**：支持删除单条或批量删除排名记录

### 3. 系统维护

- **测试API**：测试与第三方API的连接状态
- **清空数据**：清空所有排名数据（谨慎操作）

## 权限配置

系统为"QS大学排名"功能配置了以下权限：
- `system:ranking:view`：查看权限
- `system:ranking:list`：列表查询权限
- `system:ranking:add`：新增权限
- `system:ranking:edit`：编辑权限
- `system:ranking:remove`：删除权限
- `system:ranking:export`：导出权限
- `system:ranking:sync`：数据同步权限
- `system:ranking:test`：API测试权限
- `system:ranking:clear`：清空数据权限

## 注意事项

1. **API限制**：请注意API的调用频率限制，建议每秒请求不超过5次
2. **数据量**：当前API返回约1500条大学排名数据
3. **网络连接**：确保服务器能够访问外网API
4. **数据备份**：在清空数据前请做好数据备份
5. **权限管理**：根据实际需要为用户分配相应的功能权限

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 验证API密钥是否正确
   - 检查API服务是否正常

2. **数据同步失败**
   - 查看系统日志获取详细错误信息
   - 检查数据库连接是否正常
   - 验证表结构是否正确

3. **定时任务不执行**
   - 检查定时任务是否启用
   - 验证Cron表达式是否正确
   - 查看定时任务执行日志

### 日志查看

系统会记录详细的同步日志，可以通过以下方式查看：
- 应用日志文件
- 定时任务执行记录
- 系统操作日志

## 扩展说明

如需扩展功能，可以考虑：
1. 添加更多排名数据源
2. 实现数据对比分析功能
3. 添加数据变化趋势图表
4. 实现数据预警功能
5. 支持多语言显示

## 技术支持

如有问题，请联系系统管理员或查看相关技术文档。
