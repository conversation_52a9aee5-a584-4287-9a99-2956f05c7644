package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.domain.CollegeInfo;
import com.ruoyi.system.service.ICollegeInfoService;

/**
 * 全国大学高校基础信息数据同步服务
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class CollegeDataSyncService
{
    private static final Logger log = LoggerFactory.getLogger(CollegeDataSyncService.class);

    /** API基础URL */
    private static final String API_BASE_URL = "https://api.gugudata.com/metadata/college";
    
    /** API密钥 */
    private static final String APP_KEY = "257KUWTX6ZUES8994QLLN5Z5PPLDTHV5";
    
    /** 每页数据量 */
    private static final int PAGE_SIZE = 20;

    @Autowired
    private ICollegeInfoService collegeInfoService;

    /**
     * 同步全国大学高校基础信息数据
     * 
     * @return 同步结果信息
     */
    public String syncCollegeData()
    {
        log.info("开始同步全国大学高校基础信息数据");
        
        try
        {
            List<CollegeInfo> allData = new ArrayList<>();
            int pageIndex = 1;
            int totalCount = 0;
            boolean hasMoreData = true;

            // 分页获取所有数据
            while (hasMoreData)
            {
                log.info("正在获取第{}页数据", pageIndex);
                
                String param = String.format("appkey=%s&name=&pageIndex=%d&pageSize=%d", 
                    APP_KEY, pageIndex, PAGE_SIZE);
                
                String response = HttpUtils.sendGet(API_BASE_URL, param);
                
                if (StringUtils.isEmpty(response))
                {
                    log.error("API响应为空，页码：{}", pageIndex);
                    break;
                }

                JSONObject jsonResponse = JSON.parseObject(response);
                
                // 检查响应状态
                JSONObject dataStatus = jsonResponse.getJSONObject("DataStatus");
                if (dataStatus == null)
                {
                    log.error("API响应格式错误，缺少DataStatus字段");
                    break;
                }
                
                int statusCode = dataStatus.getIntValue("StatusCode");
                if (statusCode != 100)
                {
                    log.error("API返回错误状态码：{}，描述：{}", statusCode, 
                        dataStatus.getString("StatusDescription"));
                    break;
                }

                // 获取总数据量（仅第一页需要）
                if (pageIndex == 1)
                {
                    totalCount = dataStatus.getIntValue("DataTotalCount");
                    log.info("总数据量：{}", totalCount);
                }

                // 解析数据
                JSONArray dataArray = jsonResponse.getJSONArray("Data");
                if (dataArray == null || dataArray.isEmpty())
                {
                    log.info("第{}页没有数据，结束获取", pageIndex);
                    hasMoreData = false;
                    break;
                }

                // 转换数据
                List<CollegeInfo> pageData = convertJsonToEntity(dataArray);
                allData.addAll(pageData);
                
                log.info("第{}页获取到{}条数据", pageIndex, pageData.size());

                // 判断是否还有更多数据
                if (dataArray.size() < PAGE_SIZE || allData.size() >= totalCount)
                {
                    hasMoreData = false;
                }
                
                pageIndex++;
                
                // 添加延时，避免请求过于频繁
                Thread.sleep(1000);
            }

            if (allData.isEmpty())
            {
                return "同步完成，但未获取到任何数据";
            }

            // 批量保存数据
            log.info("开始保存{}条数据到数据库", allData.size());
            int result = collegeInfoService.batchInsertOrUpdate(allData);
            
            String message = String.format("同步完成，共获取%d条数据，成功保存%d条数据", 
                allData.size(), result);
            log.info(message);
            
            return message;
        }
        catch (Exception e)
        {
            log.error("同步全国大学高校基础信息数据失败", e);
            return "同步失败：" + e.getMessage();
        }
    }

    /**
     * 将JSON数据转换为实体对象
     * 
     * @param dataArray JSON数据数组
     * @return 实体对象列表
     */
    private List<CollegeInfo> convertJsonToEntity(JSONArray dataArray)
    {
        List<CollegeInfo> result = new ArrayList<>();
        Date now = DateUtils.getNowDate();
        
        for (int i = 0; i < dataArray.size(); i++)
        {
            JSONObject item = dataArray.getJSONObject(i);
            
            CollegeInfo collegeInfo = new CollegeInfo();
            
            // 基本信息
            collegeInfo.setDataId(item.getString("Id"));
            collegeInfo.setSchoolUuid(item.getString("SchoolUuid"));
            collegeInfo.setCollegeName(item.getString("CollegeName"));
            collegeInfo.setProvince(item.getString("Province"));
            collegeInfo.setCity(item.getString("City"));
            collegeInfo.setDistrict(item.getString("District"));
            collegeInfo.setCoordinate(item.getString("Coordinate"));
            collegeInfo.setCollegeType(item.getString("CollegeType"));
            
            // 特殊标识
            collegeInfo.setIs985(item.getBooleanValue("Is985"));
            collegeInfo.setIs211(item.getBooleanValue("Is211"));
            collegeInfo.setIsDualClass(item.getBooleanValue("IsDualClass"));
            
            // 分类信息
            collegeInfo.setCollegeCategory(item.getString("CollegeCategory"));
            
            // 处理标签数组
            JSONArray tagsArray = item.getJSONArray("CollegeTags");
            if (tagsArray != null && !tagsArray.isEmpty()) {
                collegeInfo.setCollegeTags(tagsArray.toJSONString());
            }
            
            collegeInfo.setEduLevel(item.getString("EduLevel"));
            collegeInfo.setCollegeProperty(item.getString("CollegeProperty"));
            collegeInfo.setCollegeCode(item.getString("CollegeCode"));
            
            // 排名信息
            Integer ranking = item.getInteger("Ranking");
            collegeInfo.setRanking(ranking != null ? ranking : 0);
            collegeInfo.setRankingInCategory(item.getString("RankingInCategory"));
            
            // 联系信息
            collegeInfo.setWebsite(item.getString("Website"));
            collegeInfo.setCallNumber(item.getString("CallNumber"));
            collegeInfo.setEmail(item.getString("Email"));
            collegeInfo.setAddress(item.getString("Address"));
            
            // 处理分校区信息
            JSONArray branchArray = item.getJSONArray("BranchList");
            if (branchArray != null && !branchArray.isEmpty()) {
                collegeInfo.setBranchList(branchArray.toJSONString());
            }
            
            collegeInfo.setCoverImage(item.getString("CoverImage"));
            collegeInfo.setIntro(item.getString("Intro"));
            collegeInfo.setExpenses(item.getString("Expenses"));
            collegeInfo.setOldName(item.getString("OldName"));
            collegeInfo.setShortName(item.getString("ShortName"));
            
            // 处理专业列表
            JSONArray majorArray = item.getJSONArray("MajorList");
            if (majorArray != null && !majorArray.isEmpty()) {
                collegeInfo.setMajorList(majorArray.toJSONString());
            }
            
            collegeInfo.setIsDeleted(item.getBooleanValue("IsDeleted"));
            
            // 设置创建和更新信息
            collegeInfo.setCreateBy("system");
            collegeInfo.setCreateTime(now);
            collegeInfo.setUpdateBy("system");
            collegeInfo.setUpdateTime(now);
            collegeInfo.setRemark("API同步数据");
            
            result.add(collegeInfo);
        }
        
        return result;
    }

    /**
     * 测试API连接
     * 
     * @return 测试结果
     */
    public String testApiConnection()
    {
        try
        {
            String param = String.format("appkey=%s&name=&pageIndex=1&pageSize=1", APP_KEY);
            String response = HttpUtils.sendGet(API_BASE_URL, param);
            
            if (StringUtils.isEmpty(response))
            {
                return "API连接失败：响应为空";
            }
            
            JSONObject jsonResponse = JSON.parseObject(response);
            JSONObject dataStatus = jsonResponse.getJSONObject("DataStatus");
            
            if (dataStatus == null)
            {
                return "API连接失败：响应格式错误";
            }
            
            int statusCode = dataStatus.getIntValue("StatusCode");
            String statusDescription = dataStatus.getString("StatusDescription");
            
            if (statusCode == 100)
            {
                int totalCount = dataStatus.getIntValue("DataTotalCount");
                return String.format("API连接成功，总数据量：%d", totalCount);
            }
            else
            {
                return String.format("API连接失败，状态码：%d，描述：%s", statusCode, statusDescription);
            }
        }
        catch (Exception e)
        {
            log.error("测试API连接失败", e);
            return "API连接失败：" + e.getMessage();
        }
    }

    /**
     * 获取数据统计信息
     * 
     * @return 统计信息
     */
    public String getDataStatistics()
    {
        try
        {
            List<CollegeInfo> provinceStats = collegeInfoService.selectCollegeCountByProvince();
            List<CollegeInfo> categoryStats = collegeInfoService.selectCollegeCountByCategory();
            List<CollegeInfo> specialStats = collegeInfoService.selectSpecialCollegeCount();
            
            StringBuilder result = new StringBuilder();
            result.append("数据统计信息：\n");
            
            result.append("\n各省份高校数量TOP10：\n");
            int count = 0;
            for (CollegeInfo stat : provinceStats) {
                if (count >= 10) break;
                result.append(String.format("%s：%d所\n", stat.getProvince(), stat.getRanking()));
                count++;
            }
            
            result.append("\n特殊高校统计：\n");
            for (CollegeInfo stat : specialStats) {
                result.append(String.format("%s：%d所\n", stat.getCollegeCategory(), stat.getRanking()));
            }
            
            return result.toString();
        }
        catch (Exception e)
        {
            log.error("获取数据统计信息失败", e);
            return "获取统计信息失败：" + e.getMessage();
        }
    }
}
