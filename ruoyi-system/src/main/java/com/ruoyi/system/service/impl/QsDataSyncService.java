package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.domain.QsUniversityRanking;
import com.ruoyi.system.service.IQsUniversityRankingService;

/**
 * QS世界大学排名数据同步服务
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class QsDataSyncService
{
    private static final Logger log = LoggerFactory.getLogger(QsDataSyncService.class);

    /** API基础URL */
    private static final String API_BASE_URL = "https://api.gugudata.com/metadata/global-university-ranking";
    
    /** API密钥 */
    private static final String APP_KEY = "3KPPMJEQK3EHL6PZDPXTGDEPMKQPZP4U";
    
    /** 每页数据量 */
    private static final int PAGE_SIZE = 20;

    @Autowired
    private IQsUniversityRankingService qsUniversityRankingService;

    /**
     * 同步QS世界大学排名数据
     * 
     * @return 同步结果信息
     */
    public String syncQsUniversityRankingData()
    {
        log.info("开始同步QS世界大学排名数据");
        
        try
        {
            List<QsUniversityRanking> allData = new ArrayList<>();
            int pageIndex = 1;
            int totalCount = 0;
            boolean hasMoreData = true;

            // 分页获取所有数据
            while (hasMoreData)
            {
                log.info("正在获取第{}页数据", pageIndex);
                
                String param = String.format("appkey=%s&name=&pageIndex=%d&pageSize=%d", 
                    APP_KEY, pageIndex, PAGE_SIZE);
                
                String response = HttpUtils.sendGet(API_BASE_URL, param);
                
                if (StringUtils.isEmpty(response))
                {
                    log.error("API响应为空，页码：{}", pageIndex);
                    break;
                }

                JSONObject jsonResponse = JSON.parseObject(response);
                
                // 检查响应状态
                JSONObject dataStatus = jsonResponse.getJSONObject("DataStatus");
                if (dataStatus == null)
                {
                    log.error("API响应格式错误，缺少DataStatus字段");
                    break;
                }
                
                int statusCode = dataStatus.getIntValue("StatusCode");
                if (statusCode != 100)
                {
                    log.error("API返回错误状态码：{}，描述：{}", statusCode, 
                        dataStatus.getString("StatusDescription"));
                    break;
                }

                // 获取总数据量（仅第一页需要）
                if (pageIndex == 1)
                {
                    totalCount = dataStatus.getIntValue("DataTotalCount");
                    log.info("总数据量：{}", totalCount);
                }

                // 解析数据
                JSONArray dataArray = jsonResponse.getJSONArray("Data");
                if (dataArray == null || dataArray.isEmpty())
                {
                    log.info("第{}页没有数据，结束获取", pageIndex);
                    hasMoreData = false;
                    break;
                }

                // 转换数据
                List<QsUniversityRanking> pageData = convertJsonToEntity(dataArray);
                allData.addAll(pageData);
                
                log.info("第{}页获取到{}条数据", pageIndex, pageData.size());

                // 判断是否还有更多数据
                if (dataArray.size() < PAGE_SIZE || allData.size() >= totalCount)
                {
                    hasMoreData = false;
                }
                
                pageIndex++;
                
                // 添加延时，避免请求过于频繁
                Thread.sleep(1000);
            }

            if (allData.isEmpty())
            {
                return "同步完成，但未获取到任何数据";
            }

            // 批量保存数据
            log.info("开始保存{}条数据到数据库", allData.size());
            int result = qsUniversityRankingService.batchInsertOrUpdate(allData);
            
            String message = String.format("同步完成，共获取%d条数据，成功保存%d条数据", 
                allData.size(), result);
            log.info(message);
            
            return message;
        }
        catch (Exception e)
        {
            log.error("同步QS世界大学排名数据失败", e);
            return "同步失败：" + e.getMessage();
        }
    }

    /**
     * 将JSON数据转换为实体对象
     * 
     * @param dataArray JSON数据数组
     * @return 实体对象列表
     */
    private List<QsUniversityRanking> convertJsonToEntity(JSONArray dataArray)
    {
        List<QsUniversityRanking> result = new ArrayList<>();
        Date now = DateUtils.getNowDate();
        
        for (int i = 0; i < dataArray.size(); i++)
        {
            JSONObject item = dataArray.getJSONObject(i);
            
            QsUniversityRanking ranking = new QsUniversityRanking();
            
            // 基本信息
            ranking.setUniversityId(item.getString("Id"));
            ranking.setUniversityName(item.getString("UniversityName"));
            ranking.setRegion(item.getString("Region"));
            ranking.setCountry(item.getString("Country"));
            ranking.setCity(item.getString("City"));
            ranking.setLogoUrl(item.getString("LogoUrl"));
            
            // 排名信息
            ranking.setRankOverall(item.getString("Rank"));
            ranking.setAcademicReputationRank(item.getString("AcademicReputationRank"));
            ranking.setAcademicReputationScore(item.getString("AcademicReputationScore"));
            ranking.setCitationsPerFacultyRank(item.getString("CitationsPerFacultyRank"));
            ranking.setCitationsPerFacultyScore(item.getString("CitationsPerFacultyScore"));
            ranking.setFacultyStudentRatioRank(item.getString("FacultyStudentRatioRank"));
            ranking.setFacultyStudentRatioScore(item.getString("FacultyStudentRatioScore"));
            ranking.setEmployerReputationRank(item.getString("EmployerReputationRank"));
            ranking.setEmployerReputationScore(item.getString("EmployerReputationScore"));
            ranking.setEmploymentOutcomesRank(item.getString("EmploymentOutcomesRank"));
            ranking.setEmploymentOutcomesScore(item.getString("EmploymentOutcomesScore"));
            ranking.setInternationalStudentRatioRank(item.getString("InternationalStudentRatioRank"));
            ranking.setInternationalStudentRatioScore(item.getString("InternationalStudentRatioScore"));
            ranking.setInternationalResearchNetworkRank(item.getString("InternationalResearchNetworkRank"));
            ranking.setInternationalResearchNetworkScore(item.getString("InternationalResearchNetworkScore"));
            ranking.setInternationalFacultyRatioRank(item.getString("InternationalFacultyRatioRank"));
            ranking.setInternationalFacultyRatioScore(item.getString("InternationalFacultyRatioScore"));
            ranking.setSustainabilityRank(item.getString("SustainabilityRank"));
            ranking.setSustainabilityScore(item.getString("SustainabilityScore"));
            
            // 设置创建和更新信息
            ranking.setCreateBy("system");
            ranking.setCreateTime(now);
            ranking.setUpdateBy("system");
            ranking.setUpdateTime(now);
            ranking.setRemark("API同步数据");
            
            result.add(ranking);
        }
        
        return result;
    }

    /**
     * 测试API连接
     * 
     * @return 测试结果
     */
    public String testApiConnection()
    {
        try
        {
            String param = String.format("appkey=%s&name=&pageIndex=1&pageSize=1", APP_KEY);
            String response = HttpUtils.sendGet(API_BASE_URL, param);
            
            if (StringUtils.isEmpty(response))
            {
                return "API连接失败：响应为空";
            }
            
            JSONObject jsonResponse = JSON.parseObject(response);
            JSONObject dataStatus = jsonResponse.getJSONObject("DataStatus");
            
            if (dataStatus == null)
            {
                return "API连接失败：响应格式错误";
            }
            
            int statusCode = dataStatus.getIntValue("StatusCode");
            String statusDescription = dataStatus.getString("StatusDescription");
            
            if (statusCode == 100)
            {
                int totalCount = dataStatus.getIntValue("DataTotalCount");
                return String.format("API连接成功，总数据量：%d", totalCount);
            }
            else
            {
                return String.format("API连接失败，状态码：%d，描述：%s", statusCode, statusDescription);
            }
        }
        catch (Exception e)
        {
            log.error("测试API连接失败", e);
            return "API连接失败：" + e.getMessage();
        }
    }
}
