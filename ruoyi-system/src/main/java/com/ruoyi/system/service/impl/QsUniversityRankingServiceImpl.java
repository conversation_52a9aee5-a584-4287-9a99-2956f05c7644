package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.QsUniversityRankingMapper;
import com.ruoyi.system.domain.QsUniversityRanking;
import com.ruoyi.system.service.IQsUniversityRankingService;
import com.ruoyi.common.core.text.Convert;

/**
 * QS世界大学排名数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class QsUniversityRankingServiceImpl implements IQsUniversityRankingService 
{
    @Autowired
    private QsUniversityRankingMapper qsUniversityRankingMapper;

    /**
     * 查询QS世界大学排名数据
     * 
     * @param id QS世界大学排名数据主键
     * @return QS世界大学排名数据
     */
    @Override
    public QsUniversityRanking selectQsUniversityRankingById(Long id)
    {
        return qsUniversityRankingMapper.selectQsUniversityRankingById(id);
    }

    /**
     * 根据大学ID查询QS世界大学排名数据
     * 
     * @param universityId 大学ID
     * @return QS世界大学排名数据
     */
    @Override
    public QsUniversityRanking selectQsUniversityRankingByUniversityId(String universityId)
    {
        return qsUniversityRankingMapper.selectQsUniversityRankingByUniversityId(universityId);
    }

    /**
     * 查询QS世界大学排名数据列表
     * 
     * @param qsUniversityRanking QS世界大学排名数据
     * @return QS世界大学排名数据
     */
    @Override
    public List<QsUniversityRanking> selectQsUniversityRankingList(QsUniversityRanking qsUniversityRanking)
    {
        return qsUniversityRankingMapper.selectQsUniversityRankingList(qsUniversityRanking);
    }

    /**
     * 新增QS世界大学排名数据
     * 
     * @param qsUniversityRanking QS世界大学排名数据
     * @return 结果
     */
    @Override
    public int insertQsUniversityRanking(QsUniversityRanking qsUniversityRanking)
    {
        qsUniversityRanking.setCreateTime(DateUtils.getNowDate());
        return qsUniversityRankingMapper.insertQsUniversityRanking(qsUniversityRanking);
    }

    /**
     * 修改QS世界大学排名数据
     * 
     * @param qsUniversityRanking QS世界大学排名数据
     * @return 结果
     */
    @Override
    public int updateQsUniversityRanking(QsUniversityRanking qsUniversityRanking)
    {
        qsUniversityRanking.setUpdateTime(DateUtils.getNowDate());
        return qsUniversityRankingMapper.updateQsUniversityRanking(qsUniversityRanking);
    }

    /**
     * 批量删除QS世界大学排名数据
     * 
     * @param ids 需要删除的QS世界大学排名数据主键
     * @return 结果
     */
    @Override
    public int deleteQsUniversityRankingByIds(String ids)
    {
        return qsUniversityRankingMapper.deleteQsUniversityRankingByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除QS世界大学排名数据信息
     * 
     * @param id QS世界大学排名数据主键
     * @return 结果
     */
    @Override
    public int deleteQsUniversityRankingById(Long id)
    {
        return qsUniversityRankingMapper.deleteQsUniversityRankingById(id);
    }

    /**
     * 批量插入或更新QS世界大学排名数据
     * 
     * @param qsUniversityRankingList QS世界大学排名数据集合
     * @return 结果
     */
    @Override
    public int batchInsertOrUpdate(List<QsUniversityRanking> qsUniversityRankingList)
    {
        if (qsUniversityRankingList == null || qsUniversityRankingList.isEmpty()) {
            return 0;
        }
        return qsUniversityRankingMapper.batchInsertOrUpdate(qsUniversityRankingList);
    }

    /**
     * 清空所有数据
     * 
     * @return 结果
     */
    @Override
    public int truncateTable()
    {
        return qsUniversityRankingMapper.truncateTable();
    }
}
