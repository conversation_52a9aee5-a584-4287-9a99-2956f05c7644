package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CollegeInfoMapper;
import com.ruoyi.system.domain.CollegeInfo;
import com.ruoyi.system.service.ICollegeInfoService;
import com.ruoyi.common.core.text.Convert;

/**
 * 全国大学高校基础信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class CollegeInfoServiceImpl implements ICollegeInfoService 
{
    @Autowired
    private CollegeInfoMapper collegeInfoMapper;

    /**
     * 查询全国大学高校基础信息
     * 
     * @param id 全国大学高校基础信息主键
     * @return 全国大学高校基础信息
     */
    @Override
    public CollegeInfo selectCollegeInfoById(Long id)
    {
        return collegeInfoMapper.selectCollegeInfoById(id);
    }

    /**
     * 根据高校UUID查询全国大学高校基础信息
     * 
     * @param schoolUuid 高校UUID
     * @return 全国大学高校基础信息
     */
    @Override
    public CollegeInfo selectCollegeInfoBySchoolUuid(String schoolUuid)
    {
        return collegeInfoMapper.selectCollegeInfoBySchoolUuid(schoolUuid);
    }

    /**
     * 查询全国大学高校基础信息列表
     * 
     * @param collegeInfo 全国大学高校基础信息
     * @return 全国大学高校基础信息
     */
    @Override
    public List<CollegeInfo> selectCollegeInfoList(CollegeInfo collegeInfo)
    {
        return collegeInfoMapper.selectCollegeInfoList(collegeInfo);
    }

    /**
     * 新增全国大学高校基础信息
     * 
     * @param collegeInfo 全国大学高校基础信息
     * @return 结果
     */
    @Override
    public int insertCollegeInfo(CollegeInfo collegeInfo)
    {
        collegeInfo.setCreateTime(DateUtils.getNowDate());
        return collegeInfoMapper.insertCollegeInfo(collegeInfo);
    }

    /**
     * 修改全国大学高校基础信息
     * 
     * @param collegeInfo 全国大学高校基础信息
     * @return 结果
     */
    @Override
    public int updateCollegeInfo(CollegeInfo collegeInfo)
    {
        collegeInfo.setUpdateTime(DateUtils.getNowDate());
        return collegeInfoMapper.updateCollegeInfo(collegeInfo);
    }

    /**
     * 批量删除全国大学高校基础信息
     * 
     * @param ids 需要删除的全国大学高校基础信息主键
     * @return 结果
     */
    @Override
    public int deleteCollegeInfoByIds(String ids)
    {
        return collegeInfoMapper.deleteCollegeInfoByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除全国大学高校基础信息信息
     * 
     * @param id 全国大学高校基础信息主键
     * @return 结果
     */
    @Override
    public int deleteCollegeInfoById(Long id)
    {
        return collegeInfoMapper.deleteCollegeInfoById(id);
    }

    /**
     * 批量插入或更新全国大学高校基础信息
     * 
     * @param collegeInfoList 全国大学高校基础信息集合
     * @return 结果
     */
    @Override
    public int batchInsertOrUpdate(List<CollegeInfo> collegeInfoList)
    {
        if (collegeInfoList == null || collegeInfoList.isEmpty()) {
            return 0;
        }
        return collegeInfoMapper.batchInsertOrUpdate(collegeInfoList);
    }

    /**
     * 清空所有数据
     * 
     * @return 结果
     */
    @Override
    public int truncateTable()
    {
        return collegeInfoMapper.truncateTable();
    }

    /**
     * 统计各省份高校数量
     * 
     * @return 统计结果
     */
    @Override
    public List<CollegeInfo> selectCollegeCountByProvince()
    {
        return collegeInfoMapper.selectCollegeCountByProvince();
    }

    /**
     * 统计各类别高校数量
     * 
     * @return 统计结果
     */
    @Override
    public List<CollegeInfo> selectCollegeCountByCategory()
    {
        return collegeInfoMapper.selectCollegeCountByCategory();
    }

    /**
     * 统计985/211/双一流高校数量
     * 
     * @return 统计结果
     */
    @Override
    public List<CollegeInfo> selectSpecialCollegeCount()
    {
        return collegeInfoMapper.selectSpecialCollegeCount();
    }
}
