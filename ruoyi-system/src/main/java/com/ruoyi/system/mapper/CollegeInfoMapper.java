package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.CollegeInfo;

/**
 * 全国大学高校基础信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface CollegeInfoMapper 
{
    /**
     * 查询全国大学高校基础信息
     * 
     * @param id 全国大学高校基础信息主键
     * @return 全国大学高校基础信息
     */
    public CollegeInfo selectCollegeInfoById(Long id);

    /**
     * 根据高校UUID查询全国大学高校基础信息
     * 
     * @param schoolUuid 高校UUID
     * @return 全国大学高校基础信息
     */
    public CollegeInfo selectCollegeInfoBySchoolUuid(String schoolUuid);

    /**
     * 查询全国大学高校基础信息列表
     * 
     * @param collegeInfo 全国大学高校基础信息
     * @return 全国大学高校基础信息集合
     */
    public List<CollegeInfo> selectCollegeInfoList(CollegeInfo collegeInfo);

    /**
     * 新增全国大学高校基础信息
     * 
     * @param collegeInfo 全国大学高校基础信息
     * @return 结果
     */
    public int insertCollegeInfo(CollegeInfo collegeInfo);

    /**
     * 修改全国大学高校基础信息
     * 
     * @param collegeInfo 全国大学高校基础信息
     * @return 结果
     */
    public int updateCollegeInfo(CollegeInfo collegeInfo);

    /**
     * 删除全国大学高校基础信息
     * 
     * @param id 全国大学高校基础信息主键
     * @return 结果
     */
    public int deleteCollegeInfoById(Long id);

    /**
     * 批量删除全国大学高校基础信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCollegeInfoByIds(String[] ids);

    /**
     * 批量插入或更新全国大学高校基础信息
     * 
     * @param collegeInfoList 全国大学高校基础信息集合
     * @return 结果
     */
    public int batchInsertOrUpdate(List<CollegeInfo> collegeInfoList);

    /**
     * 清空所有数据
     * 
     * @return 结果
     */
    public int truncateTable();

    /**
     * 统计各省份高校数量
     * 
     * @return 统计结果
     */
    public List<CollegeInfo> selectCollegeCountByProvince();

    /**
     * 统计各类别高校数量
     * 
     * @return 统计结果
     */
    public List<CollegeInfo> selectCollegeCountByCategory();

    /**
     * 统计985/211/双一流高校数量
     * 
     * @return 统计结果
     */
    public List<CollegeInfo> selectSpecialCollegeCount();
}
