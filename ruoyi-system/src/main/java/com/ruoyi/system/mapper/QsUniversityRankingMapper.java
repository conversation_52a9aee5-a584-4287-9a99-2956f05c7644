package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.QsUniversityRanking;

/**
 * QS世界大学排名数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface QsUniversityRankingMapper 
{
    /**
     * 查询QS世界大学排名数据
     * 
     * @param id QS世界大学排名数据主键
     * @return QS世界大学排名数据
     */
    public QsUniversityRanking selectQsUniversityRankingById(Long id);

    /**
     * 根据大学ID查询QS世界大学排名数据
     * 
     * @param universityId 大学ID
     * @return QS世界大学排名数据
     */
    public QsUniversityRanking selectQsUniversityRankingByUniversityId(String universityId);

    /**
     * 查询QS世界大学排名数据列表
     * 
     * @param qsUniversityRanking QS世界大学排名数据
     * @return QS世界大学排名数据集合
     */
    public List<QsUniversityRanking> selectQsUniversityRankingList(QsUniversityRanking qsUniversityRanking);

    /**
     * 新增QS世界大学排名数据
     * 
     * @param qsUniversityRanking QS世界大学排名数据
     * @return 结果
     */
    public int insertQsUniversityRanking(QsUniversityRanking qsUniversityRanking);

    /**
     * 修改QS世界大学排名数据
     * 
     * @param qsUniversityRanking QS世界大学排名数据
     * @return 结果
     */
    public int updateQsUniversityRanking(QsUniversityRanking qsUniversityRanking);

    /**
     * 删除QS世界大学排名数据
     * 
     * @param id QS世界大学排名数据主键
     * @return 结果
     */
    public int deleteQsUniversityRankingById(Long id);

    /**
     * 批量删除QS世界大学排名数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQsUniversityRankingByIds(String[] ids);

    /**
     * 批量插入或更新QS世界大学排名数据
     * 
     * @param qsUniversityRankingList QS世界大学排名数据集合
     * @return 结果
     */
    public int batchInsertOrUpdate(List<QsUniversityRanking> qsUniversityRankingList);

    /**
     * 清空所有数据
     * 
     * @return 结果
     */
    public int truncateTable();
}
