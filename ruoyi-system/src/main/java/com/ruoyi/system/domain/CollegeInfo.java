package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 全国大学高校基础信息对象 college_info
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public class CollegeInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 数据全局唯一ID */
    @Excel(name = "数据ID")
    private String dataId;

    /** 咕咕数据平台高校唯一ID */
    @Excel(name = "高校UUID")
    private String schoolUuid;

    /** 学院名称 */
    @Excel(name = "学院名称")
    private String collegeName;

    /** 学院所在省份 */
    @Excel(name = "所在省份")
    private String province;

    /** 学院所在城市 */
    @Excel(name = "所在城市")
    private String city;

    /** 学院所在区县 */
    @Excel(name = "所在区县")
    private String district;

    /** 学院地理坐标经纬度 */
    private String coordinate;

    /** 学院性质 */
    @Excel(name = "学院性质")
    private String collegeType;

    /** 是否为985院校 */
    @Excel(name = "是否985", readConverterExp = "0=否,1=是")
    private Boolean is985;

    /** 是否为211院校 */
    @Excel(name = "是否211", readConverterExp = "0=否,1=是")
    private Boolean is211;

    /** 是否为双一流院校 */
    @Excel(name = "是否双一流", readConverterExp = "0=否,1=是")
    private Boolean isDualClass;

    /** 学院类别 */
    @Excel(name = "学院类别")
    private String collegeCategory;

    /** 学院标签(JSON数组) */
    private String collegeTags;

    /** 学院学制 */
    @Excel(name = "学院学制")
    private String eduLevel;

    /** 学院资质 */
    @Excel(name = "学院资质")
    private String collegeProperty;

    /** 学院编号 */
    @Excel(name = "学院编号")
    private String collegeCode;

    /** 全国排名 */
    @Excel(name = "全国排名")
    private Integer ranking;

    /** 学院所在类别下排名 */
    @Excel(name = "类别排名")
    private String rankingInCategory;

    /** 学院官网 */
    private String website;

    /** 学院招生电话 */
    private String callNumber;

    /** 学院招生邮箱 */
    private String email;

    /** 学院地址 */
    private String address;

    /** 主/分校区名称和地址(JSON数组) */
    private String branchList;

    /** 学院校徽 */
    private String coverImage;

    /** 学院简介 */
    private String intro;

    /** 学院收费 */
    private String expenses;

    /** 学院旧称 */
    private String oldName;

    /** 学院简称 */
    private String shortName;

    /** 开设专业列表(JSON数组) */
    private String majorList;

    /** 废弃数据标识 */
    private Boolean isDeleted;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataId(String dataId) 
    {
        this.dataId = dataId;
    }

    public String getDataId() 
    {
        return dataId;
    }

    public void setSchoolUuid(String schoolUuid) 
    {
        this.schoolUuid = schoolUuid;
    }

    public String getSchoolUuid() 
    {
        return schoolUuid;
    }

    public void setCollegeName(String collegeName) 
    {
        this.collegeName = collegeName;
    }

    public String getCollegeName() 
    {
        return collegeName;
    }

    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }

    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }

    public void setDistrict(String district) 
    {
        this.district = district;
    }

    public String getDistrict() 
    {
        return district;
    }

    public void setCoordinate(String coordinate) 
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate() 
    {
        return coordinate;
    }

    public void setCollegeType(String collegeType) 
    {
        this.collegeType = collegeType;
    }

    public String getCollegeType() 
    {
        return collegeType;
    }

    public void setIs985(Boolean is985) 
    {
        this.is985 = is985;
    }

    public Boolean getIs985() 
    {
        return is985;
    }

    public void setIs211(Boolean is211) 
    {
        this.is211 = is211;
    }

    public Boolean getIs211() 
    {
        return is211;
    }

    public void setIsDualClass(Boolean isDualClass) 
    {
        this.isDualClass = isDualClass;
    }

    public Boolean getIsDualClass() 
    {
        return isDualClass;
    }

    public void setCollegeCategory(String collegeCategory) 
    {
        this.collegeCategory = collegeCategory;
    }

    public String getCollegeCategory() 
    {
        return collegeCategory;
    }

    public void setCollegeTags(String collegeTags) 
    {
        this.collegeTags = collegeTags;
    }

    public String getCollegeTags() 
    {
        return collegeTags;
    }

    public void setEduLevel(String eduLevel) 
    {
        this.eduLevel = eduLevel;
    }

    public String getEduLevel() 
    {
        return eduLevel;
    }

    public void setCollegeProperty(String collegeProperty) 
    {
        this.collegeProperty = collegeProperty;
    }

    public String getCollegeProperty() 
    {
        return collegeProperty;
    }

    public void setCollegeCode(String collegeCode) 
    {
        this.collegeCode = collegeCode;
    }

    public String getCollegeCode() 
    {
        return collegeCode;
    }

    public void setRanking(Integer ranking) 
    {
        this.ranking = ranking;
    }

    public Integer getRanking() 
    {
        return ranking;
    }

    public void setRankingInCategory(String rankingInCategory) 
    {
        this.rankingInCategory = rankingInCategory;
    }

    public String getRankingInCategory() 
    {
        return rankingInCategory;
    }

    public void setWebsite(String website) 
    {
        this.website = website;
    }

    public String getWebsite() 
    {
        return website;
    }

    public void setCallNumber(String callNumber) 
    {
        this.callNumber = callNumber;
    }

    public String getCallNumber() 
    {
        return callNumber;
    }

    public void setEmail(String email) 
    {
        this.email = email;
    }

    public String getEmail() 
    {
        return email;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setBranchList(String branchList) 
    {
        this.branchList = branchList;
    }

    public String getBranchList() 
    {
        return branchList;
    }

    public void setCoverImage(String coverImage) 
    {
        this.coverImage = coverImage;
    }

    public String getCoverImage() 
    {
        return coverImage;
    }

    public void setIntro(String intro) 
    {
        this.intro = intro;
    }

    public String getIntro() 
    {
        return intro;
    }

    public void setExpenses(String expenses) 
    {
        this.expenses = expenses;
    }

    public String getExpenses() 
    {
        return expenses;
    }

    public void setOldName(String oldName) 
    {
        this.oldName = oldName;
    }

    public String getOldName() 
    {
        return oldName;
    }

    public void setShortName(String shortName) 
    {
        this.shortName = shortName;
    }

    public String getShortName() 
    {
        return shortName;
    }

    public void setMajorList(String majorList) 
    {
        this.majorList = majorList;
    }

    public String getMajorList() 
    {
        return majorList;
    }

    public void setIsDeleted(Boolean isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Boolean getIsDeleted() 
    {
        return isDeleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataId", getDataId())
            .append("schoolUuid", getSchoolUuid())
            .append("collegeName", getCollegeName())
            .append("province", getProvince())
            .append("city", getCity())
            .append("district", getDistrict())
            .append("coordinate", getCoordinate())
            .append("collegeType", getCollegeType())
            .append("is985", getIs985())
            .append("is211", getIs211())
            .append("isDualClass", getIsDualClass())
            .append("collegeCategory", getCollegeCategory())
            .append("collegeTags", getCollegeTags())
            .append("eduLevel", getEduLevel())
            .append("collegeProperty", getCollegeProperty())
            .append("collegeCode", getCollegeCode())
            .append("ranking", getRanking())
            .append("rankingInCategory", getRankingInCategory())
            .append("website", getWebsite())
            .append("callNumber", getCallNumber())
            .append("email", getEmail())
            .append("address", getAddress())
            .append("branchList", getBranchList())
            .append("coverImage", getCoverImage())
            .append("intro", getIntro())
            .append("expenses", getExpenses())
            .append("oldName", getOldName())
            .append("shortName", getShortName())
            .append("majorList", getMajorList())
            .append("isDeleted", getIsDeleted())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
