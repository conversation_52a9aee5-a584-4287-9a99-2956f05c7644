package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * QS世界大学排名数据对象 qs_university_ranking
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public class QsUniversityRanking extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 咕咕数据平台大学唯一标识 */
    @Excel(name = "大学ID")
    private String universityId;

    /** 大学名称 */
    @Excel(name = "大学名称")
    private String universityName;

    /** 所在地区 */
    @Excel(name = "所在地区")
    private String region;

    /** 所在国家 */
    @Excel(name = "所在国家")
    private String country;

    /** 所在城市 */
    @Excel(name = "所在城市")
    private String city;

    /** 大学Logo图片地址 */
    private String logoUrl;

    /** 综合排名 */
    @Excel(name = "综合排名")
    private String rankOverall;

    /** 学术声誉排名 */
    @Excel(name = "学术声誉排名")
    private String academicReputationRank;

    /** 学术声誉得分 */
    @Excel(name = "学术声誉得分")
    private String academicReputationScore;

    /** 每名教师的引用率排名 */
    @Excel(name = "每名教师的引用率排名")
    private String citationsPerFacultyRank;

    /** 每名教师的引用率得分 */
    @Excel(name = "每名教师的引用率得分")
    private String citationsPerFacultyScore;

    /** 师生比例排名 */
    @Excel(name = "师生比例排名")
    private String facultyStudentRatioRank;

    /** 师生比例得分 */
    @Excel(name = "师生比例得分")
    private String facultyStudentRatioScore;

    /** 雇主声誉排名 */
    @Excel(name = "雇主声誉排名")
    private String employerReputationRank;

    /** 雇主声誉得分 */
    @Excel(name = "雇主声誉得分")
    private String employerReputationScore;

    /** 就业成果排名 */
    @Excel(name = "就业成果排名")
    private String employmentOutcomesRank;

    /** 就业成果得分 */
    @Excel(name = "就业成果得分")
    private String employmentOutcomesScore;

    /** 国际学生比例排名 */
    @Excel(name = "国际学生比例排名")
    private String internationalStudentRatioRank;

    /** 国际学生比例得分 */
    @Excel(name = "国际学生比例得分")
    private String internationalStudentRatioScore;

    /** 国际研究网络排名 */
    @Excel(name = "国际研究网络排名")
    private String internationalResearchNetworkRank;

    /** 国际研究网络得分 */
    @Excel(name = "国际研究网络得分")
    private String internationalResearchNetworkScore;

    /** 国际教师比例排名 */
    @Excel(name = "国际教师比例排名")
    private String internationalFacultyRatioRank;

    /** 国际教师比例得分 */
    @Excel(name = "国际教师比例得分")
    private String internationalFacultyRatioScore;

    /** 可持续发展排名 */
    @Excel(name = "可持续发展排名")
    private String sustainabilityRank;

    /** 可持续发展得分 */
    @Excel(name = "可持续发展得分")
    private String sustainabilityScore;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setUniversityId(String universityId) 
    {
        this.universityId = universityId;
    }

    public String getUniversityId() 
    {
        return universityId;
    }

    public void setUniversityName(String universityName) 
    {
        this.universityName = universityName;
    }

    public String getUniversityName() 
    {
        return universityName;
    }

    public void setRegion(String region) 
    {
        this.region = region;
    }

    public String getRegion() 
    {
        return region;
    }

    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }

    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }

    public void setLogoUrl(String logoUrl) 
    {
        this.logoUrl = logoUrl;
    }

    public String getLogoUrl() 
    {
        return logoUrl;
    }

    public void setRankOverall(String rankOverall) 
    {
        this.rankOverall = rankOverall;
    }

    public String getRankOverall() 
    {
        return rankOverall;
    }

    public void setAcademicReputationRank(String academicReputationRank) 
    {
        this.academicReputationRank = academicReputationRank;
    }

    public String getAcademicReputationRank() 
    {
        return academicReputationRank;
    }

    public void setAcademicReputationScore(String academicReputationScore) 
    {
        this.academicReputationScore = academicReputationScore;
    }

    public String getAcademicReputationScore() 
    {
        return academicReputationScore;
    }

    public void setCitationsPerFacultyRank(String citationsPerFacultyRank) 
    {
        this.citationsPerFacultyRank = citationsPerFacultyRank;
    }

    public String getCitationsPerFacultyRank() 
    {
        return citationsPerFacultyRank;
    }

    public void setCitationsPerFacultyScore(String citationsPerFacultyScore) 
    {
        this.citationsPerFacultyScore = citationsPerFacultyScore;
    }

    public String getCitationsPerFacultyScore() 
    {
        return citationsPerFacultyScore;
    }

    public void setFacultyStudentRatioRank(String facultyStudentRatioRank) 
    {
        this.facultyStudentRatioRank = facultyStudentRatioRank;
    }

    public String getFacultyStudentRatioRank() 
    {
        return facultyStudentRatioRank;
    }

    public void setFacultyStudentRatioScore(String facultyStudentRatioScore) 
    {
        this.facultyStudentRatioScore = facultyStudentRatioScore;
    }

    public String getFacultyStudentRatioScore() 
    {
        return facultyStudentRatioScore;
    }

    public void setEmployerReputationRank(String employerReputationRank) 
    {
        this.employerReputationRank = employerReputationRank;
    }

    public String getEmployerReputationRank() 
    {
        return employerReputationRank;
    }

    public void setEmployerReputationScore(String employerReputationScore) 
    {
        this.employerReputationScore = employerReputationScore;
    }

    public String getEmployerReputationScore() 
    {
        return employerReputationScore;
    }

    public void setEmploymentOutcomesRank(String employmentOutcomesRank) 
    {
        this.employmentOutcomesRank = employmentOutcomesRank;
    }

    public String getEmploymentOutcomesRank() 
    {
        return employmentOutcomesRank;
    }

    public void setEmploymentOutcomesScore(String employmentOutcomesScore) 
    {
        this.employmentOutcomesScore = employmentOutcomesScore;
    }

    public String getEmploymentOutcomesScore() 
    {
        return employmentOutcomesScore;
    }

    public void setInternationalStudentRatioRank(String internationalStudentRatioRank) 
    {
        this.internationalStudentRatioRank = internationalStudentRatioRank;
    }

    public String getInternationalStudentRatioRank() 
    {
        return internationalStudentRatioRank;
    }

    public void setInternationalStudentRatioScore(String internationalStudentRatioScore) 
    {
        this.internationalStudentRatioScore = internationalStudentRatioScore;
    }

    public String getInternationalStudentRatioScore() 
    {
        return internationalStudentRatioScore;
    }

    public void setInternationalResearchNetworkRank(String internationalResearchNetworkRank) 
    {
        this.internationalResearchNetworkRank = internationalResearchNetworkRank;
    }

    public String getInternationalResearchNetworkRank() 
    {
        return internationalResearchNetworkRank;
    }

    public void setInternationalResearchNetworkScore(String internationalResearchNetworkScore) 
    {
        this.internationalResearchNetworkScore = internationalResearchNetworkScore;
    }

    public String getInternationalResearchNetworkScore() 
    {
        return internationalResearchNetworkScore;
    }

    public void setInternationalFacultyRatioRank(String internationalFacultyRatioRank) 
    {
        this.internationalFacultyRatioRank = internationalFacultyRatioRank;
    }

    public String getInternationalFacultyRatioRank() 
    {
        return internationalFacultyRatioRank;
    }

    public void setInternationalFacultyRatioScore(String internationalFacultyRatioScore) 
    {
        this.internationalFacultyRatioScore = internationalFacultyRatioScore;
    }

    public String getInternationalFacultyRatioScore() 
    {
        return internationalFacultyRatioScore;
    }

    public void setSustainabilityRank(String sustainabilityRank) 
    {
        this.sustainabilityRank = sustainabilityRank;
    }

    public String getSustainabilityRank() 
    {
        return sustainabilityRank;
    }

    public void setSustainabilityScore(String sustainabilityScore) 
    {
        this.sustainabilityScore = sustainabilityScore;
    }

    public String getSustainabilityScore() 
    {
        return sustainabilityScore;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("universityId", getUniversityId())
            .append("universityName", getUniversityName())
            .append("region", getRegion())
            .append("country", getCountry())
            .append("city", getCity())
            .append("logoUrl", getLogoUrl())
            .append("rankOverall", getRankOverall())
            .append("academicReputationRank", getAcademicReputationRank())
            .append("academicReputationScore", getAcademicReputationScore())
            .append("citationsPerFacultyRank", getCitationsPerFacultyRank())
            .append("citationsPerFacultyScore", getCitationsPerFacultyScore())
            .append("facultyStudentRatioRank", getFacultyStudentRatioRank())
            .append("facultyStudentRatioScore", getFacultyStudentRatioScore())
            .append("employerReputationRank", getEmployerReputationRank())
            .append("employerReputationScore", getEmployerReputationScore())
            .append("employmentOutcomesRank", getEmploymentOutcomesRank())
            .append("employmentOutcomesScore", getEmploymentOutcomesScore())
            .append("internationalStudentRatioRank", getInternationalStudentRatioRank())
            .append("internationalStudentRatioScore", getInternationalStudentRatioScore())
            .append("internationalResearchNetworkRank", getInternationalResearchNetworkRank())
            .append("internationalResearchNetworkScore", getInternationalResearchNetworkScore())
            .append("internationalFacultyRatioRank", getInternationalFacultyRatioRank())
            .append("internationalFacultyRatioScore", getInternationalFacultyRatioScore())
            .append("sustainabilityRank", getSustainabilityRank())
            .append("sustainabilityScore", getSustainabilityScore())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
