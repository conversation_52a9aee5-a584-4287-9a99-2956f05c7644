<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CollegeInfoMapper">
    
    <resultMap type="CollegeInfo" id="CollegeInfoResult">
        <result property="id"    column="id"    />
        <result property="dataId"    column="data_id"    />
        <result property="schoolUuid"    column="school_uuid"    />
        <result property="collegeName"    column="college_name"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="coordinate"    column="coordinate"    />
        <result property="collegeType"    column="college_type"    />
        <result property="is985"    column="is_985"    />
        <result property="is211"    column="is_211"    />
        <result property="isDualClass"    column="is_dual_class"    />
        <result property="collegeCategory"    column="college_category"    />
        <result property="collegeTags"    column="college_tags"    />
        <result property="eduLevel"    column="edu_level"    />
        <result property="collegeProperty"    column="college_property"    />
        <result property="collegeCode"    column="college_code"    />
        <result property="ranking"    column="ranking"    />
        <result property="rankingInCategory"    column="ranking_in_category"    />
        <result property="website"    column="website"    />
        <result property="callNumber"    column="call_number"    />
        <result property="email"    column="email"    />
        <result property="address"    column="address"    />
        <result property="branchList"    column="branch_list"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="intro"    column="intro"    />
        <result property="expenses"    column="expenses"    />
        <result property="oldName"    column="old_name"    />
        <result property="shortName"    column="short_name"    />
        <result property="majorList"    column="major_list"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCollegeInfoVo">
        select id, data_id, school_uuid, college_name, province, city, district, coordinate, college_type, is_985, is_211, is_dual_class, college_category, college_tags, edu_level, college_property, college_code, ranking, ranking_in_category, website, call_number, email, address, branch_list, cover_image, intro, expenses, old_name, short_name, major_list, is_deleted, create_by, create_time, update_by, update_time, remark from college_info
    </sql>

    <select id="selectCollegeInfoList" parameterType="CollegeInfo" resultMap="CollegeInfoResult">
        <include refid="selectCollegeInfoVo"/>
        <where>  
            <if test="dataId != null  and dataId != ''"> and data_id = #{dataId}</if>
            <if test="schoolUuid != null  and schoolUuid != ''"> and school_uuid = #{schoolUuid}</if>
            <if test="collegeName != null  and collegeName != ''"> and college_name like concat('%', #{collegeName}, '%')</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="collegeType != null  and collegeType != ''"> and college_type = #{collegeType}</if>
            <if test="is985 != null"> and is_985 = #{is985}</if>
            <if test="is211 != null"> and is_211 = #{is211}</if>
            <if test="isDualClass != null"> and is_dual_class = #{isDualClass}</if>
            <if test="collegeCategory != null  and collegeCategory != ''"> and college_category = #{collegeCategory}</if>
            <if test="eduLevel != null  and eduLevel != ''"> and edu_level = #{eduLevel}</if>
            <if test="collegeProperty != null  and collegeProperty != ''"> and college_property = #{collegeProperty}</if>
            <if test="isDeleted != null"> and is_deleted = #{isDeleted}</if>
        </where>
        order by ranking ASC, college_name ASC
    </select>
    
    <select id="selectCollegeInfoById" parameterType="Long" resultMap="CollegeInfoResult">
        <include refid="selectCollegeInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectCollegeInfoBySchoolUuid" parameterType="String" resultMap="CollegeInfoResult">
        <include refid="selectCollegeInfoVo"/>
        where school_uuid = #{schoolUuid}
    </select>
        
    <insert id="insertCollegeInfo" parameterType="CollegeInfo" useGeneratedKeys="true" keyProperty="id">
        insert into college_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataId != null and dataId != ''">data_id,</if>
            <if test="schoolUuid != null and schoolUuid != ''">school_uuid,</if>
            <if test="collegeName != null and collegeName != ''">college_name,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="coordinate != null">coordinate,</if>
            <if test="collegeType != null">college_type,</if>
            <if test="is985 != null">is_985,</if>
            <if test="is211 != null">is_211,</if>
            <if test="isDualClass != null">is_dual_class,</if>
            <if test="collegeCategory != null">college_category,</if>
            <if test="collegeTags != null">college_tags,</if>
            <if test="eduLevel != null">edu_level,</if>
            <if test="collegeProperty != null">college_property,</if>
            <if test="collegeCode != null">college_code,</if>
            <if test="ranking != null">ranking,</if>
            <if test="rankingInCategory != null">ranking_in_category,</if>
            <if test="website != null">website,</if>
            <if test="callNumber != null">call_number,</if>
            <if test="email != null">email,</if>
            <if test="address != null">address,</if>
            <if test="branchList != null">branch_list,</if>
            <if test="coverImage != null">cover_image,</if>
            <if test="intro != null">intro,</if>
            <if test="expenses != null">expenses,</if>
            <if test="oldName != null">old_name,</if>
            <if test="shortName != null">short_name,</if>
            <if test="majorList != null">major_list,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataId != null and dataId != ''">#{dataId},</if>
            <if test="schoolUuid != null and schoolUuid != ''">#{schoolUuid},</if>
            <if test="collegeName != null and collegeName != ''">#{collegeName},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="coordinate != null">#{coordinate},</if>
            <if test="collegeType != null">#{collegeType},</if>
            <if test="is985 != null">#{is985},</if>
            <if test="is211 != null">#{is211},</if>
            <if test="isDualClass != null">#{isDualClass},</if>
            <if test="collegeCategory != null">#{collegeCategory},</if>
            <if test="collegeTags != null">#{collegeTags},</if>
            <if test="eduLevel != null">#{eduLevel},</if>
            <if test="collegeProperty != null">#{collegeProperty},</if>
            <if test="collegeCode != null">#{collegeCode},</if>
            <if test="ranking != null">#{ranking},</if>
            <if test="rankingInCategory != null">#{rankingInCategory},</if>
            <if test="website != null">#{website},</if>
            <if test="callNumber != null">#{callNumber},</if>
            <if test="email != null">#{email},</if>
            <if test="address != null">#{address},</if>
            <if test="branchList != null">#{branchList},</if>
            <if test="coverImage != null">#{coverImage},</if>
            <if test="intro != null">#{intro},</if>
            <if test="expenses != null">#{expenses},</if>
            <if test="oldName != null">#{oldName},</if>
            <if test="shortName != null">#{shortName},</if>
            <if test="majorList != null">#{majorList},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCollegeInfo" parameterType="CollegeInfo">
        update college_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataId != null and dataId != ''">data_id = #{dataId},</if>
            <if test="schoolUuid != null and schoolUuid != ''">school_uuid = #{schoolUuid},</if>
            <if test="collegeName != null and collegeName != ''">college_name = #{collegeName},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="coordinate != null">coordinate = #{coordinate},</if>
            <if test="collegeType != null">college_type = #{collegeType},</if>
            <if test="is985 != null">is_985 = #{is985},</if>
            <if test="is211 != null">is_211 = #{is211},</if>
            <if test="isDualClass != null">is_dual_class = #{isDualClass},</if>
            <if test="collegeCategory != null">college_category = #{collegeCategory},</if>
            <if test="collegeTags != null">college_tags = #{collegeTags},</if>
            <if test="eduLevel != null">edu_level = #{eduLevel},</if>
            <if test="collegeProperty != null">college_property = #{collegeProperty},</if>
            <if test="collegeCode != null">college_code = #{collegeCode},</if>
            <if test="ranking != null">ranking = #{ranking},</if>
            <if test="rankingInCategory != null">ranking_in_category = #{rankingInCategory},</if>
            <if test="website != null">website = #{website},</if>
            <if test="callNumber != null">call_number = #{callNumber},</if>
            <if test="email != null">email = #{email},</if>
            <if test="address != null">address = #{address},</if>
            <if test="branchList != null">branch_list = #{branchList},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="intro != null">intro = #{intro},</if>
            <if test="expenses != null">expenses = #{expenses},</if>
            <if test="oldName != null">old_name = #{oldName},</if>
            <if test="shortName != null">short_name = #{shortName},</if>
            <if test="majorList != null">major_list = #{majorList},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCollegeInfoById" parameterType="Long">
        delete from college_info where id = #{id}
    </delete>

    <delete id="deleteCollegeInfoByIds" parameterType="String">
        delete from college_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO college_info (
            data_id, school_uuid, college_name, province, city, district, coordinate, college_type,
            is_985, is_211, is_dual_class, college_category, college_tags, edu_level, college_property,
            college_code, ranking, ranking_in_category, website, call_number, email, address,
            branch_list, cover_image, intro, expenses, old_name, short_name, major_list, is_deleted,
            create_by, create_time, update_by, update_time, remark
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.dataId}, #{item.schoolUuid}, #{item.collegeName}, #{item.province}, #{item.city}, #{item.district}, #{item.coordinate}, #{item.collegeType},
                #{item.is985}, #{item.is211}, #{item.isDualClass}, #{item.collegeCategory}, #{item.collegeTags}, #{item.eduLevel}, #{item.collegeProperty},
                #{item.collegeCode}, #{item.ranking}, #{item.rankingInCategory}, #{item.website}, #{item.callNumber}, #{item.email}, #{item.address},
                #{item.branchList}, #{item.coverImage}, #{item.intro}, #{item.expenses}, #{item.oldName}, #{item.shortName}, #{item.majorList}, #{item.isDeleted},
                #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            data_id = VALUES(data_id),
            college_name = VALUES(college_name),
            province = VALUES(province),
            city = VALUES(city),
            district = VALUES(district),
            coordinate = VALUES(coordinate),
            college_type = VALUES(college_type),
            is_985 = VALUES(is_985),
            is_211 = VALUES(is_211),
            is_dual_class = VALUES(is_dual_class),
            college_category = VALUES(college_category),
            college_tags = VALUES(college_tags),
            edu_level = VALUES(edu_level),
            college_property = VALUES(college_property),
            college_code = VALUES(college_code),
            ranking = VALUES(ranking),
            ranking_in_category = VALUES(ranking_in_category),
            website = VALUES(website),
            call_number = VALUES(call_number),
            email = VALUES(email),
            address = VALUES(address),
            branch_list = VALUES(branch_list),
            cover_image = VALUES(cover_image),
            intro = VALUES(intro),
            expenses = VALUES(expenses),
            old_name = VALUES(old_name),
            short_name = VALUES(short_name),
            major_list = VALUES(major_list),
            is_deleted = VALUES(is_deleted),
            update_by = VALUES(update_by),
            update_time = VALUES(update_time),
            remark = VALUES(remark)
    </insert>

    <delete id="truncateTable">
        TRUNCATE TABLE college_info
    </delete>

    <select id="selectCollegeCountByProvince" resultMap="CollegeInfoResult">
        SELECT province, COUNT(*) as ranking FROM college_info 
        WHERE is_deleted = 0 
        GROUP BY province 
        ORDER BY ranking DESC
    </select>

    <select id="selectCollegeCountByCategory" resultMap="CollegeInfoResult">
        SELECT college_category, COUNT(*) as ranking FROM college_info 
        WHERE is_deleted = 0 AND college_category IS NOT NULL AND college_category != ''
        GROUP BY college_category 
        ORDER BY ranking DESC
    </select>

    <select id="selectSpecialCollegeCount" resultMap="CollegeInfoResult">
        SELECT 
            '985高校' as college_category, COUNT(*) as ranking FROM college_info WHERE is_985 = 1 AND is_deleted = 0
        UNION ALL
        SELECT 
            '211高校' as college_category, COUNT(*) as ranking FROM college_info WHERE is_211 = 1 AND is_deleted = 0
        UNION ALL
        SELECT 
            '双一流高校' as college_category, COUNT(*) as ranking FROM college_info WHERE is_dual_class = 1 AND is_deleted = 0
    </select>

</mapper>
