<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.QsUniversityRankingMapper">
    
    <resultMap type="QsUniversityRanking" id="QsUniversityRankingResult">
        <result property="id"    column="id"    />
        <result property="universityId"    column="university_id"    />
        <result property="universityName"    column="university_name"    />
        <result property="region"    column="region"    />
        <result property="country"    column="country"    />
        <result property="city"    column="city"    />
        <result property="logoUrl"    column="logo_url"    />
        <result property="rankOverall"    column="rank_overall"    />
        <result property="academicReputationRank"    column="academic_reputation_rank"    />
        <result property="academicReputationScore"    column="academic_reputation_score"    />
        <result property="citationsPerFacultyRank"    column="citations_per_faculty_rank"    />
        <result property="citationsPerFacultyScore"    column="citations_per_faculty_score"    />
        <result property="facultyStudentRatioRank"    column="faculty_student_ratio_rank"    />
        <result property="facultyStudentRatioScore"    column="faculty_student_ratio_score"    />
        <result property="employerReputationRank"    column="employer_reputation_rank"    />
        <result property="employerReputationScore"    column="employer_reputation_score"    />
        <result property="employmentOutcomesRank"    column="employment_outcomes_rank"    />
        <result property="employmentOutcomesScore"    column="employment_outcomes_score"    />
        <result property="internationalStudentRatioRank"    column="international_student_ratio_rank"    />
        <result property="internationalStudentRatioScore"    column="international_student_ratio_score"    />
        <result property="internationalResearchNetworkRank"    column="international_research_network_rank"    />
        <result property="internationalResearchNetworkScore"    column="international_research_network_score"    />
        <result property="internationalFacultyRatioRank"    column="international_faculty_ratio_rank"    />
        <result property="internationalFacultyRatioScore"    column="international_faculty_ratio_score"    />
        <result property="sustainabilityRank"    column="sustainability_rank"    />
        <result property="sustainabilityScore"    column="sustainability_score"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectQsUniversityRankingVo">
        select id, university_id, university_name, region, country, city, logo_url, rank_overall, academic_reputation_rank, academic_reputation_score, citations_per_faculty_rank, citations_per_faculty_score, faculty_student_ratio_rank, faculty_student_ratio_score, employer_reputation_rank, employer_reputation_score, employment_outcomes_rank, employment_outcomes_score, international_student_ratio_rank, international_student_ratio_score, international_research_network_rank, international_research_network_score, international_faculty_ratio_rank, international_faculty_ratio_score, sustainability_rank, sustainability_score, create_by, create_time, update_by, update_time, remark from qs_university_ranking
    </sql>

    <select id="selectQsUniversityRankingList" parameterType="QsUniversityRanking" resultMap="QsUniversityRankingResult">
        <include refid="selectQsUniversityRankingVo"/>
        <where>  
            <if test="universityId != null  and universityId != ''"> and university_id = #{universityId}</if>
            <if test="universityName != null  and universityName != ''"> and university_name like concat('%', #{universityName}, '%')</if>
            <if test="region != null  and region != ''"> and region = #{region}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="rankOverall != null  and rankOverall != ''"> and rank_overall = #{rankOverall}</if>
        </where>
        order by CAST(rank_overall AS UNSIGNED) ASC
    </select>
    
    <select id="selectQsUniversityRankingById" parameterType="Long" resultMap="QsUniversityRankingResult">
        <include refid="selectQsUniversityRankingVo"/>
        where id = #{id}
    </select>

    <select id="selectQsUniversityRankingByUniversityId" parameterType="String" resultMap="QsUniversityRankingResult">
        <include refid="selectQsUniversityRankingVo"/>
        where university_id = #{universityId}
    </select>
        
    <insert id="insertQsUniversityRanking" parameterType="QsUniversityRanking" useGeneratedKeys="true" keyProperty="id">
        insert into qs_university_ranking
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="universityId != null and universityId != ''">university_id,</if>
            <if test="universityName != null and universityName != ''">university_name,</if>
            <if test="region != null">region,</if>
            <if test="country != null">country,</if>
            <if test="city != null">city,</if>
            <if test="logoUrl != null">logo_url,</if>
            <if test="rankOverall != null">rank_overall,</if>
            <if test="academicReputationRank != null">academic_reputation_rank,</if>
            <if test="academicReputationScore != null">academic_reputation_score,</if>
            <if test="citationsPerFacultyRank != null">citations_per_faculty_rank,</if>
            <if test="citationsPerFacultyScore != null">citations_per_faculty_score,</if>
            <if test="facultyStudentRatioRank != null">faculty_student_ratio_rank,</if>
            <if test="facultyStudentRatioScore != null">faculty_student_ratio_score,</if>
            <if test="employerReputationRank != null">employer_reputation_rank,</if>
            <if test="employerReputationScore != null">employer_reputation_score,</if>
            <if test="employmentOutcomesRank != null">employment_outcomes_rank,</if>
            <if test="employmentOutcomesScore != null">employment_outcomes_score,</if>
            <if test="internationalStudentRatioRank != null">international_student_ratio_rank,</if>
            <if test="internationalStudentRatioScore != null">international_student_ratio_score,</if>
            <if test="internationalResearchNetworkRank != null">international_research_network_rank,</if>
            <if test="internationalResearchNetworkScore != null">international_research_network_score,</if>
            <if test="internationalFacultyRatioRank != null">international_faculty_ratio_rank,</if>
            <if test="internationalFacultyRatioScore != null">international_faculty_ratio_score,</if>
            <if test="sustainabilityRank != null">sustainability_rank,</if>
            <if test="sustainabilityScore != null">sustainability_score,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="universityId != null and universityId != ''">#{universityId},</if>
            <if test="universityName != null and universityName != ''">#{universityName},</if>
            <if test="region != null">#{region},</if>
            <if test="country != null">#{country},</if>
            <if test="city != null">#{city},</if>
            <if test="logoUrl != null">#{logoUrl},</if>
            <if test="rankOverall != null">#{rankOverall},</if>
            <if test="academicReputationRank != null">#{academicReputationRank},</if>
            <if test="academicReputationScore != null">#{academicReputationScore},</if>
            <if test="citationsPerFacultyRank != null">#{citationsPerFacultyRank},</if>
            <if test="citationsPerFacultyScore != null">#{citationsPerFacultyScore},</if>
            <if test="facultyStudentRatioRank != null">#{facultyStudentRatioRank},</if>
            <if test="facultyStudentRatioScore != null">#{facultyStudentRatioScore},</if>
            <if test="employerReputationRank != null">#{employerReputationRank},</if>
            <if test="employerReputationScore != null">#{employerReputationScore},</if>
            <if test="employmentOutcomesRank != null">#{employmentOutcomesRank},</if>
            <if test="employmentOutcomesScore != null">#{employmentOutcomesScore},</if>
            <if test="internationalStudentRatioRank != null">#{internationalStudentRatioRank},</if>
            <if test="internationalStudentRatioScore != null">#{internationalStudentRatioScore},</if>
            <if test="internationalResearchNetworkRank != null">#{internationalResearchNetworkRank},</if>
            <if test="internationalResearchNetworkScore != null">#{internationalResearchNetworkScore},</if>
            <if test="internationalFacultyRatioRank != null">#{internationalFacultyRatioRank},</if>
            <if test="internationalFacultyRatioScore != null">#{internationalFacultyRatioScore},</if>
            <if test="sustainabilityRank != null">#{sustainabilityRank},</if>
            <if test="sustainabilityScore != null">#{sustainabilityScore},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateQsUniversityRanking" parameterType="QsUniversityRanking">
        update qs_university_ranking
        <trim prefix="SET" suffixOverrides=",">
            <if test="universityId != null and universityId != ''">university_id = #{universityId},</if>
            <if test="universityName != null and universityName != ''">university_name = #{universityName},</if>
            <if test="region != null">region = #{region},</if>
            <if test="country != null">country = #{country},</if>
            <if test="city != null">city = #{city},</if>
            <if test="logoUrl != null">logo_url = #{logoUrl},</if>
            <if test="rankOverall != null">rank_overall = #{rankOverall},</if>
            <if test="academicReputationRank != null">academic_reputation_rank = #{academicReputationRank},</if>
            <if test="academicReputationScore != null">academic_reputation_score = #{academicReputationScore},</if>
            <if test="citationsPerFacultyRank != null">citations_per_faculty_rank = #{citationsPerFacultyRank},</if>
            <if test="citationsPerFacultyScore != null">citations_per_faculty_score = #{citationsPerFacultyScore},</if>
            <if test="facultyStudentRatioRank != null">faculty_student_ratio_rank = #{facultyStudentRatioRank},</if>
            <if test="facultyStudentRatioScore != null">faculty_student_ratio_score = #{facultyStudentRatioScore},</if>
            <if test="employerReputationRank != null">employer_reputation_rank = #{employerReputationRank},</if>
            <if test="employerReputationScore != null">employer_reputation_score = #{employerReputationScore},</if>
            <if test="employmentOutcomesRank != null">employment_outcomes_rank = #{employmentOutcomesRank},</if>
            <if test="employmentOutcomesScore != null">employment_outcomes_score = #{employmentOutcomesScore},</if>
            <if test="internationalStudentRatioRank != null">international_student_ratio_rank = #{internationalStudentRatioRank},</if>
            <if test="internationalStudentRatioScore != null">international_student_ratio_score = #{internationalStudentRatioScore},</if>
            <if test="internationalResearchNetworkRank != null">international_research_network_rank = #{internationalResearchNetworkRank},</if>
            <if test="internationalResearchNetworkScore != null">international_research_network_score = #{internationalResearchNetworkScore},</if>
            <if test="internationalFacultyRatioRank != null">international_faculty_ratio_rank = #{internationalFacultyRatioRank},</if>
            <if test="internationalFacultyRatioScore != null">international_faculty_ratio_score = #{internationalFacultyRatioScore},</if>
            <if test="sustainabilityRank != null">sustainability_rank = #{sustainabilityRank},</if>
            <if test="sustainabilityScore != null">sustainability_score = #{sustainabilityScore},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQsUniversityRankingById" parameterType="Long">
        delete from qs_university_ranking where id = #{id}
    </delete>

    <delete id="deleteQsUniversityRankingByIds" parameterType="String">
        delete from qs_university_ranking where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO qs_university_ranking (
            university_id, university_name, region, country, city, logo_url, rank_overall,
            academic_reputation_rank, academic_reputation_score, citations_per_faculty_rank, citations_per_faculty_score,
            faculty_student_ratio_rank, faculty_student_ratio_score, employer_reputation_rank, employer_reputation_score,
            employment_outcomes_rank, employment_outcomes_score, international_student_ratio_rank, international_student_ratio_score,
            international_research_network_rank, international_research_network_score, international_faculty_ratio_rank, international_faculty_ratio_score,
            sustainability_rank, sustainability_score, create_by, create_time, update_by, update_time, remark
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.universityId}, #{item.universityName}, #{item.region}, #{item.country}, #{item.city}, #{item.logoUrl}, #{item.rankOverall},
                #{item.academicReputationRank}, #{item.academicReputationScore}, #{item.citationsPerFacultyRank}, #{item.citationsPerFacultyScore},
                #{item.facultyStudentRatioRank}, #{item.facultyStudentRatioScore}, #{item.employerReputationRank}, #{item.employerReputationScore},
                #{item.employmentOutcomesRank}, #{item.employmentOutcomesScore}, #{item.internationalStudentRatioRank}, #{item.internationalStudentRatioScore},
                #{item.internationalResearchNetworkRank}, #{item.internationalResearchNetworkScore}, #{item.internationalFacultyRatioRank}, #{item.internationalFacultyRatioScore},
                #{item.sustainabilityRank}, #{item.sustainabilityScore}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            university_name = VALUES(university_name),
            region = VALUES(region),
            country = VALUES(country),
            city = VALUES(city),
            logo_url = VALUES(logo_url),
            rank_overall = VALUES(rank_overall),
            academic_reputation_rank = VALUES(academic_reputation_rank),
            academic_reputation_score = VALUES(academic_reputation_score),
            citations_per_faculty_rank = VALUES(citations_per_faculty_rank),
            citations_per_faculty_score = VALUES(citations_per_faculty_score),
            faculty_student_ratio_rank = VALUES(faculty_student_ratio_rank),
            faculty_student_ratio_score = VALUES(faculty_student_ratio_score),
            employer_reputation_rank = VALUES(employer_reputation_rank),
            employer_reputation_score = VALUES(employer_reputation_score),
            employment_outcomes_rank = VALUES(employment_outcomes_rank),
            employment_outcomes_score = VALUES(employment_outcomes_score),
            international_student_ratio_rank = VALUES(international_student_ratio_rank),
            international_student_ratio_score = VALUES(international_student_ratio_score),
            international_research_network_rank = VALUES(international_research_network_rank),
            international_research_network_score = VALUES(international_research_network_score),
            international_faculty_ratio_rank = VALUES(international_faculty_ratio_rank),
            international_faculty_ratio_score = VALUES(international_faculty_ratio_score),
            sustainability_rank = VALUES(sustainability_rank),
            sustainability_score = VALUES(sustainability_score),
            update_by = VALUES(update_by),
            update_time = VALUES(update_time),
            remark = VALUES(remark)
    </insert>

    <delete id="truncateTable">
        TRUNCATE TABLE qs_university_ranking
    </delete>

</mapper>
