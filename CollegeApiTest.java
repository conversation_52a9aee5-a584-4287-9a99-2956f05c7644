import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;

public class CollegeApiTest {
    public static void main(String[] args) {
        try {
            String apiUrl = "https://api.gugudata.com/metadata/college";
            String appKey = "3KPPMJEQK3EHL6PZDPXTGDEPMKQPZP4U";
            String param = "appkey=" + appKey + "&name=&pageIndex=1&pageSize=5";
            String urlString = apiUrl + "?" + param;
            
            System.out.println("Testing College API: " + urlString);
            
            URL url = new URL(urlString);
            URLConnection connection = url.openConnection();
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            StringBuilder result = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
            
            System.out.println("API Response: " + result.toString());
            
            // Parse response
            String response = result.toString();
            if (response.contains("\"StatusCode\":100")) {
                System.out.println("API connection successful!");

                // Extract total count
                if (response.contains("\"DataTotalCount\":")) {
                    int start = response.indexOf("\"DataTotalCount\":") + 17;
                    int end = response.indexOf(",", start);
                    if (end == -1) end = response.indexOf("}", start);
                    String totalCount = response.substring(start, end);
                    System.out.println("Total colleges: " + totalCount);
                }

                // Check if has data
                if (response.contains("\"Data\":[") && !response.contains("\"Data\":[]")) {
                    System.out.println("Successfully got college data");

                    // Extract first college name as example
                    if (response.contains("\"CollegeName\":\"")) {
                        int nameStart = response.indexOf("\"CollegeName\":\"") + 15;
                        int nameEnd = response.indexOf("\"", nameStart);
                        String collegeName = response.substring(nameStart, nameEnd);
                        System.out.println("Example college: " + collegeName);
                    }
                }
            } else {
                System.out.println("API connection failed or error status");
            }
            
        } catch (Exception e) {
            System.err.println("API test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
