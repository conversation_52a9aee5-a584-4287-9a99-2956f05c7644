package com.ruoyi.quartz.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.system.service.impl.QsDataSyncService;

/**
 * QS世界大学排名数据同步定时任务
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Component("qsDataSyncTask")
public class QsDataSyncTask
{
    private static final Logger log = LoggerFactory.getLogger(QsDataSyncTask.class);

    @Autowired
    private QsDataSyncService qsDataSyncService;

    /**
     * 同步QS世界大学排名数据
     */
    public void syncQsUniversityRankingData()
    {
        log.info("定时任务开始：同步QS世界大学排名数据");
        
        try
        {
            String result = qsDataSyncService.syncQsUniversityRankingData();
            log.info("定时任务完成：{}", result);
        }
        catch (Exception e)
        {
            log.error("定时任务执行失败：同步QS世界大学排名数据", e);
        }
    }

    /**
     * 测试API连接
     */
    public void testApiConnection()
    {
        log.info("定时任务开始：测试QS API连接");
        
        try
        {
            String result = qsDataSyncService.testApiConnection();
            log.info("定时任务完成：{}", result);
        }
        catch (Exception e)
        {
            log.error("定时任务执行失败：测试QS API连接", e);
        }
    }
}
