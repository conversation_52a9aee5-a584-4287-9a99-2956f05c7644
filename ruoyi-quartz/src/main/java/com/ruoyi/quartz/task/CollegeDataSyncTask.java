package com.ruoyi.quartz.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.system.service.impl.CollegeDataSyncService;

/**
 * 全国大学高校基础信息数据同步定时任务
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Component("collegeDataSyncTask")
public class CollegeDataSyncTask
{
    private static final Logger log = LoggerFactory.getLogger(CollegeDataSyncTask.class);

    @Autowired
    private CollegeDataSyncService collegeDataSyncService;

    /**
     * 同步全国大学高校基础信息数据
     */
    public void syncCollegeData()
    {
        log.info("定时任务开始：同步全国大学高校基础信息数据");
        
        try
        {
            String result = collegeDataSyncService.syncCollegeData();
            log.info("定时任务完成：{}", result);
        }
        catch (Exception e)
        {
            log.error("定时任务执行失败：同步全国大学高校基础信息数据", e);
        }
    }

    /**
     * 测试API连接
     */
    public void testApiConnection()
    {
        log.info("定时任务开始：测试高校信息API连接");
        
        try
        {
            String result = collegeDataSyncService.testApiConnection();
            log.info("定时任务完成：{}", result);
        }
        catch (Exception e)
        {
            log.error("定时任务执行失败：测试高校信息API连接", e);
        }
    }

    /**
     * 获取数据统计信息
     */
    public void getDataStatistics()
    {
        log.info("定时任务开始：获取高校数据统计信息");
        
        try
        {
            String result = collegeDataSyncService.getDataStatistics();
            log.info("定时任务完成：\n{}", result);
        }
        catch (Exception e)
        {
            log.error("定时任务执行失败：获取高校数据统计信息", e);
        }
    }
}
