package com.ruoyi.web.controller.system;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.QsUniversityRanking;
import com.ruoyi.system.service.IQsUniversityRankingService;
import com.ruoyi.system.service.impl.QsDataSyncService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * QS世界大学排名数据Controller
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Controller
@RequestMapping("/system/ranking")
public class QsUniversityRankingController extends BaseController
{
    private String prefix = "system/ranking";

    @Autowired
    private IQsUniversityRankingService qsUniversityRankingService;

    @Autowired
    private QsDataSyncService qsDataSyncService;

    @RequiresPermissions("system:ranking:view")
    @GetMapping()
    public String ranking()
    {
        return prefix + "/ranking";
    }

    /**
     * 查询QS世界大学排名数据列表
     */
    @RequiresPermissions("system:ranking:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(QsUniversityRanking qsUniversityRanking)
    {
        startPage();
        List<QsUniversityRanking> list = qsUniversityRankingService.selectQsUniversityRankingList(qsUniversityRanking);
        return getDataTable(list);
    }

    /**
     * 导出QS世界大学排名数据列表
     */
    @RequiresPermissions("system:ranking:export")
    @Log(title = "QS世界大学排名数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(QsUniversityRanking qsUniversityRanking)
    {
        List<QsUniversityRanking> list = qsUniversityRankingService.selectQsUniversityRankingList(qsUniversityRanking);
        ExcelUtil<QsUniversityRanking> util = new ExcelUtil<QsUniversityRanking>(QsUniversityRanking.class);
        return util.exportExcel(list, "QS世界大学排名数据");
    }

    /**
     * 新增QS世界大学排名数据
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存QS世界大学排名数据
     */
    @RequiresPermissions("system:ranking:add")
    @Log(title = "QS世界大学排名数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(QsUniversityRanking qsUniversityRanking)
    {
        return toAjax(qsUniversityRankingService.insertQsUniversityRanking(qsUniversityRanking));
    }

    /**
     * 修改QS世界大学排名数据
     */
    @RequiresPermissions("system:ranking:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        QsUniversityRanking qsUniversityRanking = qsUniversityRankingService.selectQsUniversityRankingById(id);
        mmap.put("qsUniversityRanking", qsUniversityRanking);
        return prefix + "/edit";
    }

    /**
     * 修改保存QS世界大学排名数据
     */
    @RequiresPermissions("system:ranking:edit")
    @Log(title = "QS世界大学排名数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(QsUniversityRanking qsUniversityRanking)
    {
        return toAjax(qsUniversityRankingService.updateQsUniversityRanking(qsUniversityRanking));
    }

    /**
     * 删除QS世界大学排名数据
     */
    @RequiresPermissions("system:ranking:remove")
    @Log(title = "QS世界大学排名数据", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(qsUniversityRankingService.deleteQsUniversityRankingByIds(ids));
    }

    /**
     * 手动同步数据
     */
    @RequiresPermissions("system:ranking:sync")
    @Log(title = "QS世界大学排名数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    @ResponseBody
    public AjaxResult syncData()
    {
        try
        {
            String result = qsDataSyncService.syncQsUniversityRankingData();
            return AjaxResult.success(result);
        }
        catch (Exception e)
        {
            logger.error("手动同步QS数据失败", e);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 测试API连接
     */
    @RequiresPermissions("system:ranking:test")
    @PostMapping("/testApi")
    @ResponseBody
    public AjaxResult testApi()
    {
        try
        {
            String result = qsDataSyncService.testApiConnection();
            return AjaxResult.success(result);
        }
        catch (Exception e)
        {
            logger.error("测试API连接失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }

    /**
     * 清空所有数据
     */
    @RequiresPermissions("system:ranking:clear")
    @Log(title = "清空QS世界大学排名数据", businessType = BusinessType.DELETE)
    @PostMapping("/clear")
    @ResponseBody
    public AjaxResult clearData()
    {
        try
        {
            int result = qsUniversityRankingService.truncateTable();
            return AjaxResult.success("清空成功，共清空 " + result + " 条数据");
        }
        catch (Exception e)
        {
            logger.error("清空QS数据失败", e);
            return AjaxResult.error("清空失败：" + e.getMessage());
        }
    }
}
