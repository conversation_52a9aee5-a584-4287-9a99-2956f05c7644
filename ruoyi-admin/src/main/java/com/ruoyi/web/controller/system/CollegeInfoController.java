package com.ruoyi.web.controller.system;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CollegeInfo;
import com.ruoyi.system.service.ICollegeInfoService;
import com.ruoyi.system.service.impl.CollegeDataSyncService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 全国大学高校基础信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Controller
@RequestMapping("/system/college")
public class CollegeInfoController extends BaseController
{
    private String prefix = "system/college";

    @Autowired
    private ICollegeInfoService collegeInfoService;

    @Autowired
    private CollegeDataSyncService collegeDataSyncService;

    @RequiresPermissions("system:college:view")
    @GetMapping()
    public String college()
    {
        return prefix + "/college";
    }

    /**
     * 查询全国大学高校基础信息列表
     */
    @RequiresPermissions("system:college:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(CollegeInfo collegeInfo)
    {
        startPage();
        List<CollegeInfo> list = collegeInfoService.selectCollegeInfoList(collegeInfo);
        return getDataTable(list);
    }

    /**
     * 导出全国大学高校基础信息列表
     */
    @RequiresPermissions("system:college:export")
    @Log(title = "全国大学高校基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(CollegeInfo collegeInfo)
    {
        List<CollegeInfo> list = collegeInfoService.selectCollegeInfoList(collegeInfo);
        ExcelUtil<CollegeInfo> util = new ExcelUtil<CollegeInfo>(CollegeInfo.class);
        return util.exportExcel(list, "全国大学高校基础信息");
    }

    /**
     * 新增全国大学高校基础信息
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存全国大学高校基础信息
     */
    @RequiresPermissions("system:college:add")
    @Log(title = "全国大学高校基础信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(CollegeInfo collegeInfo)
    {
        return toAjax(collegeInfoService.insertCollegeInfo(collegeInfo));
    }

    /**
     * 修改全国大学高校基础信息
     */
    @RequiresPermissions("system:college:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        CollegeInfo collegeInfo = collegeInfoService.selectCollegeInfoById(id);
        mmap.put("collegeInfo", collegeInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存全国大学高校基础信息
     */
    @RequiresPermissions("system:college:edit")
    @Log(title = "全国大学高校基础信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(CollegeInfo collegeInfo)
    {
        return toAjax(collegeInfoService.updateCollegeInfo(collegeInfo));
    }

    /**
     * 删除全国大学高校基础信息
     */
    @RequiresPermissions("system:college:remove")
    @Log(title = "全国大学高校基础信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(collegeInfoService.deleteCollegeInfoByIds(ids));
    }

    /**
     * 手动同步数据
     */
    @RequiresPermissions("system:college:sync")
    @Log(title = "全国大学高校基础信息同步", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    @ResponseBody
    public AjaxResult syncData()
    {
        try
        {
            String result = collegeDataSyncService.syncCollegeData();
            return AjaxResult.success(result);
        }
        catch (Exception e)
        {
            logger.error("手动同步高校数据失败", e);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 测试API连接
     */
    @RequiresPermissions("system:college:test")
    @PostMapping("/testApi")
    @ResponseBody
    public AjaxResult testApi()
    {
        try
        {
            String result = collegeDataSyncService.testApiConnection();
            return AjaxResult.success(result);
        }
        catch (Exception e)
        {
            logger.error("测试API连接失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }

    /**
     * 清空所有数据
     */
    @RequiresPermissions("system:college:clear")
    @Log(title = "清空全国大学高校基础信息", businessType = BusinessType.DELETE)
    @PostMapping("/clear")
    @ResponseBody
    public AjaxResult clearData()
    {
        try
        {
            int result = collegeInfoService.truncateTable();
            return AjaxResult.success("清空成功，共清空 " + result + " 条数据");
        }
        catch (Exception e)
        {
            logger.error("清空高校数据失败", e);
            return AjaxResult.error("清空失败：" + e.getMessage());
        }
    }

    /**
     * 获取数据统计信息
     */
    @RequiresPermissions("system:college:statistics")
    @PostMapping("/statistics")
    @ResponseBody
    public AjaxResult getStatistics()
    {
        try
        {
            String result = collegeDataSyncService.getDataStatistics();
            return AjaxResult.success(result);
        }
        catch (Exception e)
        {
            logger.error("获取统计信息失败", e);
            return AjaxResult.error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 统计页面
     */
    @RequiresPermissions("system:college:statistics")
    @GetMapping("/statistics")
    public String statistics(ModelMap mmap)
    {
        try
        {
            List<CollegeInfo> provinceStats = collegeInfoService.selectCollegeCountByProvince();
            List<CollegeInfo> categoryStats = collegeInfoService.selectCollegeCountByCategory();
            List<CollegeInfo> specialStats = collegeInfoService.selectSpecialCollegeCount();
            
            mmap.put("provinceStats", provinceStats);
            mmap.put("categoryStats", categoryStats);
            mmap.put("specialStats", specialStats);
        }
        catch (Exception e)
        {
            logger.error("获取统计数据失败", e);
        }
        
        return prefix + "/statistics";
    }
}
