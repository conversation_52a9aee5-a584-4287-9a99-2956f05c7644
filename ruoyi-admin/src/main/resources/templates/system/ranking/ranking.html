<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('QS世界大学排名数据列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>大学名称：</label>
                                <input type="text" name="universityName"/>
                            </li>
                            <li>
                                <label>所在国家：</label>
                                <input type="text" name="country"/>
                            </li>
                            <li>
                                <label>所在地区：</label>
                                <input type="text" name="region"/>
                            </li>
                            <li>
                                <label>综合排名：</label>
                                <input type="text" name="rankOverall"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:ranking:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:ranking:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:ranking:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:ranking:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="syncData()" shiro:hasPermission="system:ranking:sync">
                    <i class="fa fa-refresh"></i> 同步数据
                </a>
                <a class="btn btn-secondary" onclick="testApi()" shiro:hasPermission="system:ranking:test">
                    <i class="fa fa-link"></i> 测试API
                </a>
                <a class="btn btn-danger" onclick="clearData()" shiro:hasPermission="system:ranking:clear">
                    <i class="fa fa-trash"></i> 清空数据
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:ranking:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:ranking:remove')}]];
        var syncFlag = [[${@permission.hasPermi('system:ranking:sync')}]];
        var testFlag = [[${@permission.hasPermi('system:ranking:test')}]];
        var clearFlag = [[${@permission.hasPermi('system:ranking:clear')}]];
        var prefix = ctx + "system/ranking";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "QS世界大学排名数据",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'universityName',
                    title: '大学名称',
                    sortable: true
                },
                {
                    field: 'rankOverall',
                    title: '综合排名',
                    sortable: true
                },
                {
                    field: 'country',
                    title: '所在国家'
                },
                {
                    field: 'region',
                    title: '所在地区'
                },
                {
                    field: 'city',
                    title: '所在城市'
                },
                {
                    field: 'academicReputationRank',
                    title: '学术声誉排名'
                },
                {
                    field: 'academicReputationScore',
                    title: '学术声誉得分'
                },
                {
                    field: 'employerReputationRank',
                    title: '雇主声誉排名'
                },
                {
                    field: 'employerReputationScore',
                    title: '雇主声誉得分'
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        /* 同步数据 */
        function syncData() {
            if (!syncFlag) {
                $.modal.alertWarning("您没有同步数据的权限！");
                return;
            }
            
            $.modal.confirm("确认要从API同步最新的QS世界大学排名数据吗？", function() {
                $.modal.loading("正在同步数据，请稍候...");
                $.ajax({
                    url: prefix + "/sync",
                    type: "post",
                    dataType: "json",
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.closeLoading();
                        $.modal.alertError("同步数据失败，请联系管理员！");
                    }
                });
            });
        }

        /* 测试API连接 */
        function testApi() {
            if (!testFlag) {
                $.modal.alertWarning("您没有测试API的权限！");
                return;
            }
            
            $.modal.loading("正在测试API连接，请稍候...");
            $.ajax({
                url: prefix + "/testApi",
                type: "post",
                dataType: "json",
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg);
                    } else {
                        $.modal.alertError(result.msg);
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.alertError("测试API连接失败，请联系管理员！");
                }
            });
        }

        /* 清空数据 */
        function clearData() {
            if (!clearFlag) {
                $.modal.alertWarning("您没有清空数据的权限！");
                return;
            }
            
            $.modal.confirm("确认要清空所有QS世界大学排名数据吗？此操作不可恢复！", function() {
                $.modal.loading("正在清空数据，请稍候...");
                $.ajax({
                    url: prefix + "/clear",
                    type: "post",
                    dataType: "json",
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.closeLoading();
                        $.modal.alertError("清空数据失败，请联系管理员！");
                    }
                });
            });
        }
    </script>
</body>
</html>
