<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('全国大学高校基础信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>学院名称：</label>
                                <input type="text" name="collegeName"/>
                            </li>
                            <li>
                                <label>所在省份：</label>
                                <select name="province" th:with="type=${@dict.getType('sys_province')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>所在城市：</label>
                                <input type="text" name="city"/>
                            </li>
                            <li>
                                <label>学院性质：</label>
                                <select name="collegeType">
                                    <option value="">所有</option>
                                    <option value="公办">公办</option>
                                    <option value="民办">民办</option>
                                    <option value="中外合作办学">中外合作办学</option>
                                </select>
                            </li>
                            <li>
                                <label>985院校：</label>
                                <select name="is985">
                                    <option value="">所有</option>
                                    <option value="true">是</option>
                                    <option value="false">否</option>
                                </select>
                            </li>
                            <li>
                                <label>211院校：</label>
                                <select name="is211">
                                    <option value="">所有</option>
                                    <option value="true">是</option>
                                    <option value="false">否</option>
                                </select>
                            </li>
                            <li>
                                <label>双一流：</label>
                                <select name="isDualClass">
                                    <option value="">所有</option>
                                    <option value="true">是</option>
                                    <option value="false">否</option>
                                </select>
                            </li>
                            <li>
                                <label>学院类别：</label>
                                <select name="collegeCategory">
                                    <option value="">所有</option>
                                    <option value="综合类">综合类</option>
                                    <option value="理工类">理工类</option>
                                    <option value="师范类">师范类</option>
                                    <option value="财经类">财经类</option>
                                    <option value="医药类">医药类</option>
                                    <option value="农林类">农林类</option>
                                    <option value="政法类">政法类</option>
                                    <option value="艺术类">艺术类</option>
                                    <option value="体育类">体育类</option>
                                    <option value="语言类">语言类</option>
                                    <option value="民族类">民族类</option>
                                    <option value="军事类">军事类</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:college:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:college:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:college:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:college:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="syncData()" shiro:hasPermission="system:college:sync">
                    <i class="fa fa-refresh"></i> 同步数据
                </a>
                <a class="btn btn-secondary" onclick="testApi()" shiro:hasPermission="system:college:test">
                    <i class="fa fa-link"></i> 测试API
                </a>
                <a class="btn btn-primary" onclick="showStatistics()" shiro:hasPermission="system:college:statistics">
                    <i class="fa fa-bar-chart"></i> 数据统计
                </a>
                <a class="btn btn-danger" onclick="clearData()" shiro:hasPermission="system:college:clear">
                    <i class="fa fa-trash"></i> 清空数据
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:college:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:college:remove')}]];
        var syncFlag = [[${@permission.hasPermi('system:college:sync')}]];
        var testFlag = [[${@permission.hasPermi('system:college:test')}]];
        var clearFlag = [[${@permission.hasPermi('system:college:clear')}]];
        var statisticsFlag = [[${@permission.hasPermi('system:college:statistics')}]];
        var prefix = ctx + "system/college";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "全国大学高校基础信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'collegeName',
                    title: '学院名称',
                    sortable: true,
                    formatter: function(value, row, index) {
                        var actions = [];
                        if (row.coverImage) {
                            actions.push('<img src="' + row.coverImage + '" style="width:20px;height:20px;margin-right:5px;" onerror="this.style.display=\'none\'">');
                        }
                        actions.push('<span>' + value + '</span>');
                        return actions.join('');
                    }
                },
                {
                    field: 'province',
                    title: '所在省份'
                },
                {
                    field: 'city',
                    title: '所在城市'
                },
                {
                    field: 'collegeType',
                    title: '学院性质'
                },
                {
                    field: 'collegeCategory',
                    title: '学院类别'
                },
                {
                    field: 'ranking',
                    title: '全国排名',
                    sortable: true
                },
                {
                    field: 'is985',
                    title: '985',
                    formatter: function(value, row, index) {
                        return value ? '<span class="label label-success">是</span>' : '<span class="label label-default">否</span>';
                    }
                },
                {
                    field: 'is211',
                    title: '211',
                    formatter: function(value, row, index) {
                        return value ? '<span class="label label-success">是</span>' : '<span class="label label-default">否</span>';
                    }
                },
                {
                    field: 'isDualClass',
                    title: '双一流',
                    formatter: function(value, row, index) {
                        return value ? '<span class="label label-success">是</span>' : '<span class="label label-default">否</span>';
                    }
                },
                {
                    field: 'website',
                    title: '官网',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<a href="' + value + '" target="_blank"><i class="fa fa-external-link"></i></a>';
                        }
                        return '';
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        /* 同步数据 */
        function syncData() {
            if (!syncFlag) {
                $.modal.alertWarning("您没有同步数据的权限！");
                return;
            }
            
            $.modal.confirm("确认要从API同步最新的全国大学高校基础信息数据吗？", function() {
                $.modal.loading("正在同步数据，请稍候...");
                $.ajax({
                    url: prefix + "/sync",
                    type: "post",
                    dataType: "json",
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.closeLoading();
                        $.modal.alertError("同步数据失败，请联系管理员！");
                    }
                });
            });
        }

        /* 测试API连接 */
        function testApi() {
            if (!testFlag) {
                $.modal.alertWarning("您没有测试API的权限！");
                return;
            }
            
            $.modal.loading("正在测试API连接，请稍候...");
            $.ajax({
                url: prefix + "/testApi",
                type: "post",
                dataType: "json",
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg);
                    } else {
                        $.modal.alertError(result.msg);
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.alertError("测试API连接失败，请联系管理员！");
                }
            });
        }

        /* 显示数据统计 */
        function showStatistics() {
            if (!statisticsFlag) {
                $.modal.alertWarning("您没有查看统计的权限！");
                return;
            }
            
            var url = prefix + "/statistics";
            $.modal.openTab("数据统计", url);
        }

        /* 清空数据 */
        function clearData() {
            if (!clearFlag) {
                $.modal.alertWarning("您没有清空数据的权限！");
                return;
            }
            
            $.modal.confirm("确认要清空所有全国大学高校基础信息数据吗？此操作不可恢复！", function() {
                $.modal.loading("正在清空数据，请稍候...");
                $.ajax({
                    url: prefix + "/clear",
                    type: "post",
                    dataType: "json",
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.closeLoading();
                        $.modal.alertError("清空数据失败，请联系管理员！");
                    }
                });
            });
        }
    </script>
</body>
</html>
